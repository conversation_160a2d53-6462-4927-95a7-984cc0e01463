# Environment Variables

# API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Alternative Model Configuration (for local models)
# LOCAL_MODEL_URL=http://localhost:11434/v1
# LOCAL_MODEL_NAME=qwen:7b

# Database Configuration
DATABASE_URL=sqlite:///./ai_customer_service.db
# For PostgreSQL: postgresql://user:password@localhost/ai_customer_service

# Security Configuration
SECRET_KEY=your_super_secret_key_here_change_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Vector Store Configuration
VECTOR_STORE_PATH=./data/vectors
EMBEDDING_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2

# Knowledge Base Configuration
KNOWLEDGE_BASE_PATH=./data/knowledge_base
MAX_CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Application Configuration
APP_NAME=SVW AI Customer Service
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=[".pdf", ".txt", ".docx", ".md"]

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
