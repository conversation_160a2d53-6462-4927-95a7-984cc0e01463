"""
大语言模型服务
支持多种LLM提供商
"""
from typing import List, Optional, Dict, Any
from abc import ABC, abstractmethod
import asyncio
import httpx
import structlog

from openai import AsyncOpenAI

from core.config import settings
from core.security import key_manager
from utils.exceptions import LLMError, ConfigurationError
from services.schemas import VectorSearchResult

logger = structlog.get_logger(__name__)


class LLMClient(ABC):
    """LLM客户端抽象基类"""
    
    @abstractmethod
    async def generate_answer(
        self,
        question: str,
        context_documents: List[VectorSearchResult],
        **kwargs
    ) -> str:
        """生成回答"""
        pass


class OpenAIClient(LLMClient):
    """OpenAI客户端"""
    
    def __init__(self):
        self.model = settings.LLM_MODEL
        self.max_tokens = settings.LLM_MAX_TOKENS
        self.temperature = settings.LLM_TEMPERATURE
        self.base_url = "https://api.openai.com/v1"
    
    async def generate_answer(
        self,
        question: str,
        context_documents: List[VectorSearchResult],
        **kwargs
    ) -> str:
        """使用OpenAI生成回答"""
        try:
            # 获取API密钥
            api_key = await key_manager.get_api_key("openai")
            if not api_key:
                raise ConfigurationError("OpenAI API密钥未配置")
            
            # 构建上下文
            context = self._build_context(context_documents)
            
            # 构建提示词
            prompt = self._build_prompt(question, context)
            
            # 调用OpenAI API
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": self.model,
                        "messages": [
                            {
                                "role": "system",
                                "content": "你是一个专业的客服助手，请根据提供的上下文信息回答用户问题。"
                            },
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        "max_tokens": self.max_tokens,
                        "temperature": self.temperature
                    },
                    timeout=30.0
                )
            
            if response.status_code != 200:
                raise LLMError(f"OpenAI API调用失败: {response.status_code} - {response.text}")
            
            result = response.json()
            answer = result["choices"][0]["message"]["content"].strip()
            
            logger.info(
                "OpenAI回答生成成功",
                model=self.model,
                question_length=len(question),
                answer_length=len(answer),
                context_docs=len(context_documents)
            )
            
            return answer
            
        except Exception as e:
            logger.error("OpenAI回答生成失败", error=str(e))
            raise LLMError(f"OpenAI回答生成失败: {e}")
    
    def _build_context(self, documents: List[VectorSearchResult]) -> str:
        """构建上下文信息"""
        if not documents:
            return "没有找到相关的参考信息。"
        
        context_parts = []
        for i, doc in enumerate(documents, 1):
            context_parts.append(f"参考信息{i}：{doc.content}")
        
        return "\n\n".join(context_parts)
    
    def _build_prompt(self, question: str, context: str) -> str:
        """构建提示词"""
        return f"""请根据以下参考信息回答用户问题。如果参考信息不足以回答问题，请诚实地说明。

        参考信息：
        {context}

        用户问题：{question}

        请提供准确、有帮助的回答："""


class AnthropicClient(LLMClient):
    """Anthropic Claude客户端"""
    
    def __init__(self):
        self.model = settings.LLM_MODEL
        self.max_tokens = settings.LLM_MAX_TOKENS
        self.temperature = settings.LLM_TEMPERATURE
        self.base_url = "https://api.anthropic.com/v1"
    
    async def generate_answer(
        self,
        question: str,
        context_documents: List[VectorSearchResult],
        **kwargs
    ) -> str:
        """使用Anthropic Claude生成回答"""
        try:
            # 获取API密钥
            api_key = await key_manager.get_api_key("anthropic")
            if not api_key:
                raise ConfigurationError("Anthropic API密钥未配置")
            
            # 构建上下文和提示词
            context = self._build_context(context_documents)
            prompt = self._build_prompt(question, context)
            
            # 调用Anthropic API
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/messages",
                    headers={
                        "x-api-key": api_key,
                        "Content-Type": "application/json",
                        "anthropic-version": "2023-06-01"
                    },
                    json={
                        "model": self.model,
                        "max_tokens": self.max_tokens,
                        "temperature": self.temperature,
                        "messages": [
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ]
                    },
                    timeout=30.0
                )
            
            if response.status_code != 200:
                raise LLMError(f"Anthropic API调用失败: {response.status_code} - {response.text}")
            
            result = response.json()
            answer = result["content"][0]["text"].strip()
            
            logger.info(
                "Anthropic回答生成成功",
                model=self.model,
                question_length=len(question),
                answer_length=len(answer),
                context_docs=len(context_documents)
            )
            
            return answer
            
        except Exception as e:
            logger.error("Anthropic回答生成失败", error=str(e))
            raise LLMError(f"Anthropic回答生成失败: {e}")
    
    def _build_context(self, documents: List[VectorSearchResult]) -> str:
        """构建上下文信息"""
        if not documents:
            return "没有找到相关的参考信息。"
        
        context_parts = []
        for i, doc in enumerate(documents, 1):
            context_parts.append(f"参考信息{i}：{doc.content}")
        
        return "\n\n".join(context_parts)
    
    def _build_prompt(self, question: str, context: str) -> str:
        """构建提示词"""
        return f"""请根据以下参考信息回答用户问题。如果参考信息不足以回答问题，请诚实地说明。

参考信息：
{context}

用户问题：{question}

请提供准确、有帮助的回答："""


class VolcengineClient(LLMClient):
    """火山引擎客户端"""
    
    def __init__(self):
        self.model = settings.LLM_MODEL
        self.max_tokens = settings.LLM_MAX_TOKENS
        self.temperature = settings.LLM_TEMPERATURE
        self.base_url = "https://api.anthropic.com/v1"
        self.client = OpenAI(base_url="<BASE_URL>",
                             api_key=os.environ.get("ARK_API_KEY")
)
    
    async def generate_answer(
        self,
        question: str,
        context_documents: List[VectorSearchResult],
        **kwargs
    ) -> str:
        """使用Anthropic Claude生成回答"""
        try:
            # 获取API密钥
            api_key = await key_manager.get_api_key("anthropic")
            if not api_key:
                raise ConfigurationError("Anthropic API密钥未配置")
            
            # 构建上下文和提示词
            context = self._build_context(context_documents)
            prompt = self._build_prompt(question, context)
            
            # 调用Anthropic API
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/messages",
                    headers={
                        "x-api-key": api_key,
                        "Content-Type": "application/json",
                        "anthropic-version": "2023-06-01"
                    },
                    json={
                        "model": self.model,
                        "max_tokens": self.max_tokens,
                        "temperature": self.temperature,
                        "messages": [
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ]
                    },
                    timeout=30.0
                )
            
            if response.status_code != 200:
                raise LLMError(f"Anthropic API调用失败: {response.status_code} - {response.text}")
            
            result = response.json()
            answer = result["content"][0]["text"].strip()
            
            logger.info(
                "Anthropic回答生成成功",
                model=self.model,
                question_length=len(question),
                answer_length=len(answer),
                context_docs=len(context_documents)
            )
            
            return answer
            
        except Exception as e:
            logger.error("Anthropic回答生成失败", error=str(e))
            raise LLMError(f"Anthropic回答生成失败: {e}")
    
    def _build_context(self, documents: List[VectorSearchResult]) -> str:
        """构建上下文信息"""
        if not documents:
            return "没有找到相关的参考信息。"
        
        context_parts = []
        for i, doc in enumerate(documents, 1):
            context_parts.append(f"参考信息{i}：{doc.content}")
        
        return "\n\n".join(context_parts)
    
    def _build_prompt(self, question: str, context: str) -> str:
        """构建提示词"""
        return f"""请根据以下参考信息回答用户问题。如果参考信息不足以回答问题，请诚实地说明。

参考信息：
{context}

用户问题：{question}

请提供准确、有帮助的回答："""


class LLMService:
    """LLM服务 - 统一的大模型调用接口"""
    
    def __init__(self):
        self.provider = settings.LLM_PROVIDER.lower()
        self.client = self._create_client()
    
    def _create_client(self) -> LLMClient:
        """根据配置创建对应的LLM客户端"""
        if self.provider == "openai":
            return OpenAIClient()
        elif self.provider == "anthropic":
            return AnthropicClient()
        elif self.provider == "volcengine":
            return VolcengineClient()
        else:
            raise ConfigurationError(f"不支持的LLM提供商: {self.provider}")
    
    async def generate_answer(
        self,
        question: str,
        context_documents: List[VectorSearchResult],
        **kwargs
    ) -> str:
        """
        生成回答
        
        Args:
            question: 用户问题
            context_documents: 上下文文档列表
            **kwargs: 其他参数
            
        Returns:
            生成的回答
        """
        try:
            answer = await self.client.generate_answer(
                question=question,
                context_documents=context_documents,
                **kwargs
            )
            
            logger.info(
                "LLM回答生成完成",
                provider=self.provider,
                question_length=len(question),
                answer_length=len(answer)
            )
            
            return answer
            
        except Exception as e:
            logger.error("LLM回答生成失败", provider=self.provider, error=str(e))
            raise


# 全局LLM服务实例
llm_service = LLMService()
