import requests
import json
from config.config import config


class QASearchClient:
    def __init__(self):
        self.url = config.VECTOR_URL
        self.collection = config.COLLECTION_NAME
        self.database = config.DATABASE_NAME
        self.top_k = config.TOP_K_FAQ
        self.similarity_threshold_faq = config.SIMILARITY_THRESHOLD_FAQ
        self.similarity_threshold_llm = config.SIMILARITY_THRESHOLD_LLM
        self.return_of_not_found = config.RETURN_OF_NOT_FOUND

    def search_qa(self, query):
        """对于严肃问题，检索相似度最高的一条答案，如果相似度符合阈值，则返回"""
        payload = {
            "text": query,
            "top_k": 1,
            "collection": self.collection,
            "database": self.database
        }
        headers = {"Content-Type": "application/json"}

        result_search = {}
        try:
            response = requests.post(self.url, data=json.dumps(payload, ensure_ascii=False), headers=headers)
            response_json = response.json()

            similarity_rate = response_json["results"][0]["similarity"]
            if response_json["results"] and similarity_rate >= self.similarity_threshold_faq:
                question_standard = response_json["results"][0]["metadata"]["question"]
                answer = response_json["results"][0]["metadata"]["answer"]
                result_search["question_standard"] = question_standard
                result_search["answer"] = answer
            else:
                question_standard = response_json["results"][0]["metadata"]["question"]
                answer = self.return_of_not_found
                result_search["question_standard"] = question_standard
                result_search["answer"] = answer
            result_search["similarity_rate"] = similarity_rate

        except Exception as e:
            print(f"❌向量检索报错: {e}")
            result_search["question_standard"] = ''
            result_search["answer"] = self.return_of_not_found
            result_search["similarity_rate"] = 0

        return result_search

    def search_qa_k(self, query):
        """检索相似度最高的前top_k条答案，如果相似度符合阈值，则返回"""
        payload = {
            "text": query,
            "top_k": self.top_k,
            "collection": self.collection,
            "database": self.database
        }
        headers = {"Content-Type": "application/json"}

        results_search = []
        try:
            response = requests.post(self.url, data=json.dumps(payload, ensure_ascii=False), headers=headers)
            response_json = response.json()

            answers = []
            for i in range(len(response_json["results"])):
                if response_json["results"][i]["similarity"] >= self.similarity_threshold_llm:
                    question_standard = response_json["results"][i]["metadata"]["question"]
                    answer = response_json["results"][i]["metadata"]["answer"]
                    similarity_rate = response_json["results"][i]["similarity"]
                    if answer in answers:
                        continue
                    answers.append(answer)
                    results_search.append({"question_standard": question_standard, "answer": answer, "similarity_rate": similarity_rate})

            if len(answers) > 0:
                return results_search
            else:
                return self.return_of_not_found

        except Exception as e:
            print(f"❌向量检索报错: {e}")
            return self.return_of_not_found


if __name__ == "__main__":
    # 初始化客户端
    client = QASearchClient()
    result_search= client.search_qa("ID车有倒车摄像头清洗功能吗？")

    print(result_search)
