"""
向量数据库服务
支持双重向量检索策略
"""
import json
import requests
from typing import List, Optional
import asyncio

from core.config import settings, vector_db_1_config, vector_db_2_config
from utils.exceptions import VectorDBError
from services.schemas import VectorSearchResult
from core.logging import setup_logging, vector_logger


class VectorService:
    """向量服务 - 实现双重检索策略"""
    
    def __init__(self):
        self.preset_config = vector_db_1_config
        self.knowledge_config = vector_db_2_config
        pass
    
    async def search_faq(self, query: str) -> List[VectorSearchResult]:
        """
        第一步：搜索预设答案
        
        Args:
            query: 用户问题
            
        Returns:
            如果找到高相似度的预设答案则返回，否则返回None
        """
        try:
            url = "http://copilot.csvw.com/rag_service/api/v1/search"
            payload = {
                    "text": query,
                    "top_k": 6,
                    "collection": "FaqQuestionIndex", 
                    "database": "SVWServiceTest", 
                    }
            headers = {"Content-Type": "application/json"}
            payload_str = json.dumps(payload, ensure_ascii=False)
            response = requests.post(url, data=payload_str, headers=headers)
            results = response.json()
            vector_logger.info(
                    "FAQ向量检索结果",
                    query=query,
                    results=results
            )
        
            if 'results' in results:
                filtered_results = [
                    result for result in results['results']
                    if result['similarity'] >= self.preset_config.threshold
                ]
                
                vector_logger.info(
                    "根据阈值过滤后的FAQ向量检索结果",
                    query=query,
                    results=filtered_results,
                    threshold=self.preset_config.threshold
                )
                
                # 如果filtered_results不为空，取相似度最高的一条
                if filtered_results:
                    final_results = [sorted(filtered_results,key=lambda x: -x['similarity'])[0]]
                else:
                    final_results = []
                
                vector_logger.info(
                    "最终FAQ向量检索结果",
                    query=query,
                    results=final_results,
                    threshold=self.preset_config.threshold
                )
                
                return final_results
            
            else:
                vector_logger.info(
                    "向量接口返回异常",
                    query=query,
                    results=results
                )
                return []
            
        except Exception as e:
            vector_logger.error("FAQ向量检索逻辑异常", error=str(e))
            # 不抛出异常，继续后续流程
            return []
    
    async def search_knowledge(self, query: str) -> List[VectorSearchResult]:
        """
        第二步：搜索知识库
        
        Args:
            query: 用户问题
            
        Returns:
            相关文档列表
        """
        try:
            url = "http://copilot.csvw.com/rag_service/api/v1/search"
            payload = {
                    "text": query,
                    "top_k": 6,
                    "collection": "test11111111", 
                    "database": "lkz_test_V3", 
                    }
            headers = {"Content-Type": "application/json"}
            payload_str = json.dumps(payload, ensure_ascii=False)
            response = requests.post(url, data=payload_str, headers=headers)
            results = response.json()
            vector_logger.info(
                    "知识向量检索结果",
                    query=query,
                    results=results
            )
            if 'results' in results:
                filtered_results = [
                    result for result in results['results']
                    if result['similarity'] >= self.knowledge_config.threshold
                ]
                
                vector_logger.info(
                    "根据阈值过滤后的知识向量检索结果",
                    query=query,
                    results=filtered_results,
                    threshold=self.knowledge_config.threshold
                )
                
                # 知识相似度按从高到低排序
                if filtered_results:
                    final_results = sorted(filtered_results,key=lambda x: -x['similarity'])
                else:
                    final_results = []
                
                return final_results
            
            else:
                vector_logger.info(
                    "向量接口返回异常",
                    query=query,
                    results=results
                )
                return []
            
        except Exception as e:
            vector_logger.error("知识向量检索逻辑异常", error=str(e))
            # raise VectorDBError(f"知识库搜索失败: {e}")
            return []
    
    async def dual_search(self, query: str) -> tuple[Optional[VectorSearchResult], List[VectorSearchResult]]:
        """
        执行双重搜索策略
        
        Args:
            query: 用户问题
            
        Returns:
            (预设答案结果, 知识库搜索结果)
        """
        # 并发执行两个搜索
        preset_task = self.search_preset_answers(query)
        knowledge_task = self.search_knowledge_base(query)
        
        preset_result, knowledge_results = await asyncio.gather(
            preset_task,
            knowledge_task,
            return_exceptions=True
        )
        
        # 处理异常
        if isinstance(preset_result, Exception):
            logger.error("预设答案搜索异常", error=str(preset_result))
            preset_result = None
        
        if isinstance(knowledge_results, Exception):
            logger.error("知识库搜索异常", error=str(knowledge_results))
            knowledge_results = []
        
        return preset_result, knowledge_results


# 全局向量服务实例
vector_service = VectorService()


if __name__ == "__main__":
    # 创建异步事件循环
    async def main():
        result = await vector_service.search_faq('你好')
        print("搜索结果:", result)
    
    # 启动事件循环
    import asyncio
    asyncio.run(main())