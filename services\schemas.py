"""
Pydantic数据模型定义
用于API请求/响应的数据验证和序列化
"""
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, field_validator
from typing import List, Optional, Dict, Any


class ResponseSource(str, Enum):
    """回答来源枚举"""
    PRESET = "preset"  # 预设答案
    GENERATED = "generated"  # LLM生成


# 接口请求内容
class ChatRequest(BaseModel):
    """接口请求schema"""
    app_code: str = Field(..., description="应用编码")
    user_id: str = Field(..., description="用户ID")
    conversation_id: Optional[str] = Field(None, description="对话ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    question: str = Field(..., min_length=1, description="用户问题")
    # history_num: int = Field(0, ge=0, description="历史对话轮次")
    
    @field_validator('question')
    def validate_question(cls, v):
        """验证问题内容"""
        if not v.strip():
            raise ValueError('问题不能为空')
        return v.strip()


class ChatResponse(BaseModel):
    """问题回答响应模型"""
    app_code: str = Field(..., description="应用编码")
    user_id: str = Field(..., description="用户ID")
    conversation_id: str = Field(..., description="对话ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    question: str = Field(..., min_length=1, description="用户问题")
    answer: str = Field(..., description="回答内容")
    # source: ResponseSource = Field(..., description="回答来源")


class QuestionType(str, Enum):
    """问题类型枚举"""
    GENERAL = "general"  # 一般问题
    KNOWLEDGE = "knowledge"  # 知识查询问题





class VectorSearchRequest(BaseModel):
    """向量搜索请求模型"""
    query: str = Field(..., description="搜索查询")
    collection: str = Field(..., description="集合名称")
    top_k: int = Field(default=5, ge=1, le=20, description="返回结果数量")
    threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="相似度阈值")


# 响应模型
class VectorSearchResult(BaseModel):
    """向量搜索结果"""
    id: str = Field(..., description="文档ID")
    content: str = Field(..., description="文档内容")
    score: float = Field(..., description="相似度分数")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元数据")


class VectorSearchResponse(BaseModel):
    """向量搜索响应"""
    results: List[VectorSearchResult] = Field(default_factory=list, description="搜索结果")
    total: int = Field(..., description="结果总数")
    query: str = Field(..., description="搜索查询")
    collection: str = Field(..., description="集合名称")





class ErrorResponse(BaseModel):
    """错误响应模型"""
    error_code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    detail: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")


# 配置管理模型
class LLMConfig(BaseModel):
    """LLM配置模型"""
    provider: str = Field(..., description="提供商")
    model: str = Field(..., description="模型名称")
    max_tokens: int = Field(default=1000, description="最大token数")
    temperature: float = Field(default=0.7, description="温度参数")
    api_key: Optional[str] = Field(None, description="API密钥")


class VectorDBConfig(BaseModel):
    """向量数据库配置模型"""
    host: str = Field(..., description="主机地址")
    port: int = Field(..., description="端口")
    collection: str = Field(..., description="集合名称")
    similarity_threshold: float = Field(..., description="相似度阈值")
    top_k: int = Field(default=5, description="返回结果数量")


# 数据库模型相关
class ApiKeyCreate(BaseModel):
    """API密钥创建模型"""
    provider: str = Field(..., description="提供商名称")
    key_type: str = Field(default="default", description="密钥类型")
    key_value: str = Field(..., description="密钥值")
    description: Optional[str] = Field(None, description="描述")


class ApiKeyResponse(BaseModel):
    """API密钥响应模型"""
    id: int = Field(..., description="密钥ID")
    provider: str = Field(..., description="提供商名称")
    key_type: str = Field(..., description="密钥类型")
    description: Optional[str] = Field(None, description="描述")
    is_active: bool = Field(..., description="是否激活")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class ConversationLog(BaseModel):
    """对话日志模型"""
    session_id: str = Field(..., description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    question: str = Field(..., description="用户问题")
    answer: str = Field(..., description="系统回答")
    source: ResponseSource = Field(..., description="回答来源")
    confidence: float = Field(..., description="置信度")
    response_time: float = Field(..., description="响应时间")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


# 健康检查模型
class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field(..., description="应用版本")
    components: Dict[str, str] = Field(default_factory=dict, description="组件状态")


# 指标模型
class MetricsResponse(BaseModel):
    """指标响应模型"""
    total_requests: int = Field(..., description="总请求数")
    successful_requests: int = Field(..., description="成功请求数")
    failed_requests: int = Field(..., description="失败请求数")
    average_response_time: float = Field(..., description="平均响应时间")
    preset_answer_rate: float = Field(..., description="预设答案命中率")
    generated_answer_rate: float = Field(..., description="生成答案比率")
    timestamp: datetime = Field(default_factory=datetime.now, description="统计时间")



if __name__ == '__main__':
    context = RequestContext(
        user_id='123',
        conversation_id='456',
        session_id='789',
        app_code='app001',
        question='你好',
        history_num=10 
    )
    print(context.model_dump())