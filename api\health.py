"""
健康检查相关API端点
"""
import time
from datetime import datetime
from fastapi import APIRouter, HTTPException
import structlog

from config.config import config
from services.schemas import HealthCheckResponse, MetricsResponse

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get(
    "/ping",
    summary="简单ping检查",
    description="最简单的存活检查"
)
async def ping():
    """简单的ping检查"""
    return {
        "message": "pong",
        "timestamp": time.time(),
        "version": config.APP_VERSION
    }
