"""
日志配置模块
提供结构化日志记录功能
"""
import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

import structlog

from config.config import config


def setup_logging() -> None:
    """设置应用日志配置"""

    # 确保日志目录存在
    log_file_path = Path(config.LOG_FILE_PATH)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)

    # 获取根日志器并清除现有处理器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()

    # 设置日志级别
    log_level = getattr(logging, config.LOG_LEVEL.upper())
    root_logger.setLevel(log_level)

    # 创建文件处理器（带轮转）
    file_handler = logging.handlers.RotatingFileHandler(
        filename=config.LOG_FILE_PATH,
        maxBytes=_parse_size(config.LOG_MAX_SIZE),
        backupCount=config.LOG_BACKUP_COUNT,
        encoding='utf-8'
    )

    file_handler.setLevel(log_level)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)

    # 设置格式器
    if config.LOG_FORMAT.lower() == "json":
        # 使用structlog专用处理器
        formatter = structlog.stdlib.ProcessorFormatter(
            processor=structlog.processors.JSONRenderer(ensure_ascii=False),
            foreign_pre_chain=[
                structlog.stdlib.add_log_level,
                structlog.stdlib.add_logger_name,
            ],
        )
    else:
        formatter = structlog.stdlib.ProcessorFormatter(
            processor=structlog.dev.ConsoleRenderer(
                colors=sys.stdout.isatty()
                # 移除已废弃的 key_order 参数
            ),
            foreign_pre_chain=[
                structlog.stdlib.add_log_level,
                structlog.stdlib.add_logger_name,
                # 添加时间戳处理器到预处理器链
                structlog.processors.TimeStamper(fmt="iso"),
            ],
        )

    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到根日志器
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)


    # 配置structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            # 添加调用上下文处理器
            structlog.processors.CallsiteParameterAdder(
                [
                    structlog.processors.CallsiteParameter.FILENAME,
                    structlog.processors.CallsiteParameter.LINENO,
                    structlog.processors.CallsiteParameter.FUNC_NAME,
                ]
            ),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            # 添加字典合并处理器
            # structlog.processors.merge_contextvars,
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def _parse_size(size_str: str) -> int:
    """解析大小字符串为字节数"""
    size_str = size_str.upper()
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)


class LoggerMixin:
    """日志混入类，为其他类提供日志功能"""

    @property
    def logger(self):
        """获取结构化日志器"""
        return structlog.get_logger(self.__class__.__name__)


def get_logger(name: Optional[str] = None) -> structlog.BoundLogger:
    """获取命名日志器"""
    return structlog.get_logger(name)


# 预定义的日志器
api_logger = get_logger("api")
service_logger = get_logger("service")
db_logger = get_logger("database")
vector_logger = get_logger("vector")
llm_logger = get_logger("llm")
security_logger = get_logger("security")
