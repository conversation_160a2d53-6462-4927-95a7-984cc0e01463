import os
import sys
# 添加上上层目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from config.config import config
from core.dialog.rag.qa_search import QASearchClient
from core.dialog.rag.llm_prepare import LLMPrepare
import traceback

class Rag:
    def __init__(self):
        self.return_of_not_found = config.RETURN_OF_NOT_FOUND
        self.id_sp_qa_llm = config.ID_SP_QA_LLM
        self.id_sp_query_change = config.ID_SP_QUERY_CHANGE
        self.qa_search_client = QASearchClient()
        self.llm_prepare = LLMPrepare()
        self.similarity_threshold_llm = config.SIMILARITY_THRESHOLD_LLM
        self.similarity_threshold_recommend = config.SIMILARITY_THRESHOLD_RECOMMEND
    async def get_faq_answer_llm(self, query):
        """对检索到的top_k条答案进行RAG，返回最终答案，如果没有答案则返回推荐问"""
        answer = ''
        recommend_questions = []

        if type(query) != str:
            prompt = self.llm_prepare.prompt_maker_query_change({"messages": query})
            query = self.llm_prepare.get_completion(prompt, self.id_sp_query_change)
            print("query:", query)

        res_retrieval = self.qa_search_client.search_qa_k(query)
        print("res_retrieval:", res_retrieval)

        if res_retrieval == self.return_of_not_found:
            answer = res_retrieval
        else:
            # 推荐问，如果RAG有答案，推荐问题不返回，否则返回
            recommend_questions = [item['question_standard'] for item in res_retrieval if
                               item['similarity_rate'] > self.similarity_threshold_recommend]
            try:
                prompt = self.llm_prepare.prompt_maker({"query": query, "answers": res_retrieval})
                answer = await self.llm_prepare.get_completion(prompt, self.id_sp_qa_llm)
                if self.return_of_not_found in answer:
                    answer = self.return_of_not_found
                else:
                    recommend_questions = []  #RAG有答案返回则不返回推荐问题
            except Exception as e:
                print(f"❌ RAG过程报错: {traceback.format_exc()}")
                answer = self.return_of_not_found

        return {
            'query': query,
            'answer': answer,
            'recommend_questions': recommend_questions,
            'res_retrieval': res_retrieval
        }


# 示例使用
if __name__ == "__main__":
    import asyncio
    async def main():
        rag = Rag()
        query = {
                    "messages": [
                        # {"role": "user", "content": "ID车有倒车摄像头清洗功能吗？"},
                        # {"role": "assistant", "content": "有的"},
                        {"role": "user", "content": "如何使用呢"}
                    ]
                }
        response = await rag.get_faq_answer_llm(query)
        print(response)
    asyncio.run(main())