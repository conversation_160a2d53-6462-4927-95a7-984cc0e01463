import os
from typing import Optional
from pathlib import Path
from pydantic import Field


class BaseConfig:
    """基础配置类，包含所有环境共有的配置项"""

    # 项目基本信息
    # PROJECT_NAME: str = "vw-brand"
    # VERSION: str = "1.0.0"

    # 应用基础配置
    APP_NAME: str = "上汽大众智能客服系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    LOG_LEVEL: str = "DEBUG"

    # 配置文件目录
    PROJECT_ROOT: str = Path(__file__).parent.parent

    # 大模型配置
    API_KEY: str = os.environ.get("VOC_API_KEY", "d66bfe48-4dcd-4dfa-92a6-5ee743d8cb01")
    # ENDPOINT: str = os.environ.get("VOC_ENDPOINT", "default-endpoint")
    ENDPOINT: str = 'ep-20250124154731-kws4s'

    # prompt缓存ID文件名配置
    ID_SP_QA_LLM: str = "id_sp_qa_llm"
    ID_SP_QUERY_CHANGE: str = "id_sp_query_change"

    # 向量检索配置
    TOP_K_FAQ: int = 5
    SIMILARITY_THRESHOLD_FAQ: float = 0.85
    SIMILARITY_THRESHOLD_LLM: float = 0.4
    SIMILARITY_THRESHOLD_RECOMMEND: float = 0.7
    RETURN_OF_NOT_FOUND: str = "无匹配答案"

    # 数据配置
    DATA_DIR: str = os.path.join(PROJECT_ROOT, "data")
    DATA_PATH: str = os.path.join(DATA_DIR, "file_name")

    # 缓存配置
    CACHE_ID_DIR: str = os.path.join(PROJECT_ROOT, r'core\dialog\rag\cache')

    # 日志配置
    # LOG_LEVEL: str = "INFO"  # 可选: DEBUG, INFO, WARNING, ERROR, CRITICAL
    # LOG_DIR: str = os.path.join(PROJECT_ROOT, "logs")
    # LOG_FILE: str = f"{PROJECT_NAME}.log"

    # 日志配置
    LOG_FILE_PATH: str = "logs/app.log"
    LOG_MAX_SIZE: str = "10MB"
    LOG_BACKUP_COUNT: int = 5
    LOG_FORMAT: str = "json"


    # API配置
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_WORKERS: int = 1
    API_TIMEOUT: int = 60

    def __init__(self) -> None:
        """初始化配置，确保目录存在"""
        pass
        # self._create_directories()

    # def _create_directories(self) -> None:
    #     """创建配置中指定的目录"""
    #     for dir_path in [self.LOG_DIR, self.DATA_DIR]:
    #         if not os.path.exists(dir_path):
    #             os.makedirs(dir_path, exist_ok=True)


class DevelopmentConfig(BaseConfig):
    """开发环境配置"""

    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"

    # 向量检索配置
    VECTOR_URL: str = "http://copilot.csvw.com/rag_service/api/v1/search"
    COLLECTION_NAME: str = "FaqRewriteQuestionIndex"
    DATABASE_NAME: str = "SVWServiceTest"

    # 开发环境数据库配置
    DB_HOST: str = "localhost"
    DB_PORT: int = 5432
    DB_NAME: str = "ai_dev_db"
    DB_USER: str = "dev_user"
    DB_PASSWORD: str = os.environ.get("DB_PASSWORD", "dev_password")


class ProductionConfig(BaseConfig):
    """生产环境配置"""

    DEBUG: bool = False
    LOG_LEVEL: str = "WARNING"

    # 向量检索配置
    VECTOR_URL: str = "xxx"
    COLLECTION_NAME: str = "xxx"
    DATABASE_NAME: str = "xxx"

    # 生产环境数据库配置
    DB_HOST: str = os.environ.get("DB_HOST", "prod-db-server")
    DB_PORT: int = int(os.environ.get("DB_PORT", 5432))
    DB_NAME: str = os.environ.get("DB_NAME", "ai_prod_db")
    DB_USER: str = os.environ.get("DB_USER", "prod_user")
    DB_PASSWORD: str = os.environ.get("DB_PASSWORD", "prod_password")

# 根据环境变量选择配置
def get_config(env: Optional[str] = None) -> BaseConfig:
    """根据环境变量获取配置类实例"""
    if env is None:
        env = os.environ.get("ENV", "development")
        print(f"使用环境配置: {env}")

    config_mapping = {
        "development": DevelopmentConfig,
        "production": ProductionConfig,
    }

    if env not in config_mapping:
        raise ValueError(f"环境配置 {env} 不存在，可选值: {list(config_mapping.keys())}")

    return config_mapping[env]()


# 默认配置实例
config = get_config("development")
