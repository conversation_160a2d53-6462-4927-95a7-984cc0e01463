import os
import json
import httpx
from datetime import datetime, timedelta


import os
import sys
# 添加上上层目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from config.config import config
from openai import OpenAI
from core.prompts import PromptConfig

def check_date_difference(target_date_str, date_format="%Y-%m-%d %H:%M:%S"):
    """
    检查当前时间与指定日期之间的差距是否超过7天
    参数:
    target_date_str (str): 目标日期字符串
    date_format (str): 日期格式，默认为 "%Y-%m-%d %H:%M:%S"
    返回:
    bool: 如果差距超过7天返回True，否则返回False
    """
    # 将字符串转换为datetime对象
    target_date = datetime.strptime(target_date_str, date_format)
    # 获取当前时间
    current_time = datetime.now()
    # 计算时间差
    time_difference = current_time - target_date
    # 判断是否超过7天
    return time_difference > timedelta(days=7)

class LLMPrepare:
    def __init__(self):
        self.path_root = config.PROJECT_ROOT
        self.cache_id_path = config.CACHE_ID_DIR
        self.api_key = config.API_KEY
        self.ep = config.ENDPOINT
        self.return_of_not_found = config.RETURN_OF_NOT_FOUND
        self.client = OpenAI(base_url="https://ark.cn-beijing.volces.com/api/v3/",api_key=self.api_key)
        self.url_context_create = "https://ark.cn-beijing.volces.com/api/v3/context/create"
        self.url_chat_completion = "https://ark.cn-beijing.volces.com/api/v3/context/chat/completions"

    def prompt_maker(self, params):
        """根据模板生成 Prompt"""
        sp = PromptConfig.sp_qa_llm
        up = PromptConfig.up_qa_llm.format(**params, return_of_not_found=self.return_of_not_found)
        total_prompt = sp+ "<|split|>"+up
        return total_prompt

    def prompt_maker_query_change(self, params):
        """问题改写prompt生成
        输入的query为json格式：
        { "context": [ { "sender": "user", "content": "xxx" }, { "sender": "bot", "content": "可以的" } ], "query": "xxx" }
        """
        sp = PromptConfig.sp_query_change
        up = PromptConfig.up_query_change.format(** params)
        total_prompt = sp+ "<|split|>"+up
        return total_prompt

    async def create_context(self, system_context):
        """创建上下文缓存，获得缓存id字段后，在ContextChatCompletions-上下文缓存对话中使用。"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        payload = {
            "model": self.ep,
            "messages": [
                {
                    "role": "system",
                    "content": system_context
                }
            ],
            "mode": "common_prefix",  # 前缀缓存
            # 缓存有效期为7天
            "ttl": 604800,
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.url_context_create,
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                return response.json()['id']
        except httpx.HTTPError as e:
            print(f"创建缓存错误: {e}")
            return None

    async def chat_completion(self, user_context, context_id):
        """向大模型发起带上下文缓存的请求。在发起之前，需要调用ContextCreate-创建上下文缓存，并获取到缓存的id。"""
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        payload = {
            "context_id": context_id,  # 上下文缓存的ID
            "model": self.ep,
            "temperature": 0,
            "top_p": 0,
            "messages": [
                {
                    "role": "user",
                    "content": user_context
                }
            ]
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.url_chat_completion,
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                return response.json()['choices'][0]['message']['content']
        except httpx.HTTPError as e:
            print(f"对话请求错误: {e}")
            return None

    async def get_completion(self, prompt, id_name):
        """调用大模型获取生成结果"""
        if "<|split|>" in prompt:
            sp,up = prompt.split("<|split|>")
        else:
            raise ValueError("must have system to cache")

        if os.path.exists(self.cache_id_path + f"/{id_name}.json"):
            with open(self.cache_id_path + f"/{id_name}.json", "r", encoding="utf-8") as f:
                id_file_content = json.load(f)
                created_time = id_file_content["created_time"]
                # 如果差距超过7天返回True，否则返回False
                if check_date_difference(created_time):
                    os.remove(self.cache_id_path + f"/{id_name}.json")
                    os.makedirs(self.cache_id_path, exist_ok=True)
                    created_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    context_id = await self.create_context(sp)
                    with open(self.cache_id_path + f"/{id_name}.json", "w", encoding="utf-8") as f:
                        json.dump({"id": context_id, "created_time": created_time}, f)
                else:
                    context_id = id_file_content["id"]
        else:
            os.makedirs(self.cache_id_path, exist_ok=True)
            created_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            context_id = await self.create_context(sp)
            with open(self.cache_id_path + f"/{id_name}.json", "w", encoding="utf-8") as f:
                json.dump({"id": context_id, "created_time": created_time}, f)

        try:
            return await self.chat_completion(up, context_id)
        except Exception as e:
            print(f"❌调用大模型报错: {e}")
            return self.return_of_not_found


# 示例使用
if __name__ == "__main__":
    import asyncio
    async def main():
        llm = LLMPrepare()
        query = {
                    "messages": [
                        {"role": "user", "content": "ID车有倒车摄像头清洗功能吗？"},
                        {"role": "assistant", "content": "有的"},
                        {"role": "user", "content": "如何使用呢"}
                    ]
                }
        prompt = llm.prompt_maker_query_change({"messages": query})
        response = await llm.get_completion(prompt, "id_sp_query_change")
        print(response)
    asyncio.run(main())
