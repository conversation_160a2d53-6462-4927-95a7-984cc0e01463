"""
问答相关API端点
"""
from fastapi import APIRouter, HTTPException
import structlog

from utils.logging import api_logger

from services.schemas import (
    ChatRequest, ChatResponse
)
from services.chat_service import chat_service
from utils.exceptions import CustomException

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.post(
    "/chat",
    response_model=ChatResponse,
    summary="对话接口",
    description="用户发送问题，服务获取历史对话，并回答"
)
async def chat(request: ChatRequest) -> ChatResponse:
    """
    根据用户问题返回答案

    - **app_code**: 应用编码，用于区分调用方（必填）
    - **conversation_id**: 对话id（非必填，如果不提供会自动生成）
    - **user_id**: 用户ID（必填）
    - **session_id**: 会话ID（非必填，如果不提供会自动生成）
    - **question**: 用户问题（必填）
    
    """
    try:
        api_logger.info(
            "收到问答请求",
            app_code=request.app_code,
            conversation_id=request.conversation_id,
            user_id=request.user_id,
            session_id=request.session_id,
            question=request.question
        )
        
        response = await chat_service.chat_message(request)
        
        api_logger.info(
            "回答请求处理完成",
            app_code=response.app_code,
            conversation_id=response.conversation_id,
            user_id=response.user_id,
            session_id=response.session_id,
            question=response.question,
            answer=response.answer
        )
        
        return response
        
    except CustomException as e:
        logger.error(
            "问答请求处理失败",
            error_code=e.error_code,
            message=e.message,
            detail=e.detail
        )
        raise HTTPException(
            status_code=e.status_code,
            detail={
                "error_code": e.error_code,
                "message": e.message,
                "detail": e.detail
            }
        )
    except Exception as e:
        logger.error("问答请求处理异常", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error_code": "INTERNAL_ERROR",
                "message": "系统内部错误",
                "detail": str(e)
            }
        )

