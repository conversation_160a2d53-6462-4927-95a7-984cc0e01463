"""
问答服务 - 核心业务逻辑
实现双重检索 + LLM生成的完整流程
"""
import time
import uuid
import structlog

import os
import sys
# 添加上上层目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


from utils.logging import LoggerMixin, service_logger
from services.schemas import (
    ChatRequest, ChatResponse
)
from core.dialog.rag.qa_search import QASearchClient
from core.dialog.rag.rag import Rag
from config.config import config
# from services.llm_service import llm_service
# from app.db.database import get_db_session
# from app.db.models import ConversationLog


class ChatService(LoggerMixin):
    """对话服务"""
    
    def __init__(self):
        pass
    
    async def chat_message(self, request: ChatRequest) -> ChatResponse:
        """
        根据用户问题返回结果
        
        Args:
            request: 问题请求
            
        Returns:
            问题响应
        """
        start_time = time.time()
        conversation_id = request.conversation_id or str(uuid.uuid4())
        session_id = request.session_id or str(uuid.uuid4())
        
        try:
            service_logger.info(
                "开始处理问题",
                app_code = request.app_code,
                user_id = request.user_id,
                conversation_id = conversation_id,
                session_id = session_id,
                question = request.question,
            )
            
            # 第一步：搜索预设答案
            qa_search_client = QASearchClient()
            faq_result = qa_search_client.search_qa(request.question)
            
            if faq_result.get('answer') != config.RETURN_OF_NOT_FOUND:
                # 找到预设答案，直接返回
                response_time = time.time() - start_time
                
                response = ChatResponse(
                    app_code = request.app_code,
                    user_id = request.user_id,
                    conversation_id = conversation_id,
                    session_id = session_id,
                    question = request.question,
                    answer = faq_result.get('answer'),
                )
                
                print('response', response)
                
                service_logger.info(
                    "预置faq回答",
                    app_code = request.app_code,
                    user_id = request.user_id,
                    conversation_id = conversation_id,
                    session_id = session_id,
                    question = request.question,
                    answer = faq_result.get('answer'),
                    question_standard = faq_result.get('question_standard'),
                    similarity_rate = faq_result.get('similarity_rate')
                )
                
                return response
            

            # 第二步：Rag检索
            rag = Rag()
            results = await rag.get_faq_answer_llm(request.question)
            
            response = ChatResponse(
                    answer=results.get('answer'),
                    app_code = request.app_code,
                    user_id = request.user_id,
                    conversation_id = conversation_id,
                    session_id = session_id,
                    question = request.question
                )
            
            
            service_logger.info(
                "LLM回答生成完成",
                answer=results.get('answer'),
                app_code = request.app_code,
                user_id = request.user_id,
                conversation_id = conversation_id,
                session_id = session_id,
                question = request.question,
                res_retrieval = results.get('res_retrieval'),
                recommend_questions = results.get('recommend_questions')
            )
            
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            
            service_logger.error(
                "问题处理失败",
                app_code = request.app_code,
                user_id = request.user_id,
                conversation_id = conversation_id,
                session_id = session_id,
                error=str(e),
                response_time=response_time,
                exc_info=True
            )
            
            # 返回错误响应
            error_response = ChatResponse(
                answer="抱歉，系统暂时无法处理您的问题，请稍后重试或联系人工客服。",
                app_code = request.app_code,
                user_id = request.user_id,
                conversation_id = conversation_id,
                session_id = session_id,
                question = request.question
            )
            
            # try:
            #     await self._log_conversation(request, error_response)
            # except Exception as log_error:
            #     self.logger.error("记录对话日志失败", error=str(log_error))
            
            return error_response



# 全局问答服务实例
chat_service = ChatService()


if __name__ == "__main__":
    import asyncio
    from services.schemas import ChatRequest
    from services.chat_service import chat_service

    async def test_chat_service():
        # 构造请求参数
        request = ChatRequest(
            app_code="VW_CHATBOT",
            user_id="user_12345",
            question="安全座椅怎么安装",
            # question="你好",
            # 以下参数可选
            conversation_id="conversation_001",
            session_id="session_20230801"
        )
        
        # 调用服务
        response = await chat_service.chat_message(request)
        
        # 打印响应结果
        print(f"回答内容: {response.answer}")
        print(f"会话ID: {response.session_id}")

    asyncio.run(test_chat_service())
