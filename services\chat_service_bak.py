"""
问答服务 - 核心业务逻辑
实现双重检索 + LLM生成的完整流程
"""
import time
import uuid
import structlog

from core.logging import LoggerMixin, service_logger
from services.schemas import (
    ChatRequest, ChatResponse
)
from services.vector_service import vector_service
# from services.llm_service import llm_service
# from app.db.database import get_db_session
# from app.db.models import ConversationLog

logger = structlog.get_logger(__name__)


class ChatService(LoggerMixin):
    """对话服务"""
    
    def __init__(self):
        self.vector_service = vector_service
        # self.llm_client = llm_service
    
    async def chat_message(self, request: ChatRequest) -> ChatResponse:
        """
        根据用户问题返回结果
        
        Args:
            request: 问题请求
            
        Returns:
            问题响应
        """
        start_time = time.time()
        conversation_id = request.conversation_id or str(uuid.uuid4())
        session_id = request.session_id or str(uuid.uuid4())
        processing_steps = []
        
        try:
            service_logger.info(
                "开始处理问题",
                question=request.question,
                user_id=request.user_id,
                session_id=session_id
            )
            
            # 第一步：搜索预设答案
            processing_steps.append("搜索预设答案")
            faq_result = await self.vector_service.search_faq(request.question)
            
            if faq_result:
                # 找到预设答案，直接返回
                processing_steps.append("找到预设答案")
                response_time = time.time() - start_time
                
                response = ChatResponse(
                    app_code = request.app_code,
                    user_id = request.user_id,
                    conversation_id = conversation_id,
                    session_id = session_id,
                    question = request.question,
                    answer = faq_result.content,
                )
                
                # 记录对话日志
                await self._log_conversation(request, response)
                
                self.logger.info(
                    "预设答案处理完成",
                    session_id=session_id,
                    confidence=preset_result.score,
                    response_time=response_time
                )
                
                return response
            
            # 第二步：搜索知识库
            processing_steps.append("搜索知识库")
            knowledge_results = await self.vector_service.search_knowledge_base(request.question)
            
            if not knowledge_results:
                # 没有找到相关文档
                processing_steps.append("未找到相关文档")
                response_time = time.time() - start_time
                
                response = QuestionResponse(
                    answer="抱歉，我没有找到相关的信息来回答您的问题。请您提供更多详细信息或联系人工客服。",
                    source=ResponseSource.GENERATED,
                    confidence=0.0,
                    response_time=response_time,
                    session_id=session_id,
                    vector_results=[],
                    processing_steps=processing_steps
                )
                
                await self._log_conversation(request, response)
                
                self.logger.info(
                    "未找到相关文档",
                    session_id=session_id,
                    response_time=response_time
                )
                
                return response
            
            # 第三步：使用LLM生成回答
            processing_steps.append("LLM生成回答")
            generated_answer = await self.llm_service.generate_answer(
                question=request.question,
                context_documents=knowledge_results
            )
            
            # 计算置信度（基于检索结果的平均分数）
            confidence = sum(result.score for result in knowledge_results) / len(knowledge_results)
            
            response_time = time.time() - start_time
            processing_steps.append("回答生成完成")
            
            response = QuestionResponse(
                answer=generated_answer,
                source=ResponseSource.GENERATED,
                confidence=confidence,
                response_time=response_time,
                session_id=session_id,
                vector_results=knowledge_results,
                llm_model=self.llm_service.provider,
                processing_steps=processing_steps
            )
            
            # 记录对话日志
            await self._log_conversation(request, response)
            
            self.logger.info(
                "LLM回答生成完成",
                session_id=session_id,
                confidence=confidence,
                response_time=response_time,
                context_docs=len(knowledge_results)
            )
            
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            processing_steps.append(f"处理失败: {str(e)}")
            
            self.logger.error(
                "问题处理失败",
                session_id=session_id,
                error=str(e),
                response_time=response_time,
                exc_info=True
            )
            
            # 返回错误响应
            error_response = QuestionResponse(
                answer="抱歉，系统暂时无法处理您的问题，请稍后重试或联系人工客服。",
                source=ResponseSource.GENERATED,
                confidence=0.0,
                response_time=response_time,
                session_id=session_id,
                processing_steps=processing_steps
            )
            
            try:
                await self._log_conversation(request, error_response)
            except Exception as log_error:
                self.logger.error("记录对话日志失败", error=str(log_error))
            
            return error_response



# 全局问答服务实例
chat_service = ChatService()
